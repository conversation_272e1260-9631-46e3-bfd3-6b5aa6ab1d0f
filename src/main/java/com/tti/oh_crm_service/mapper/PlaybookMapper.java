package com.tti.oh_crm_service.mapper;

import com.tti.oh_crm_service.entity.*;
import com.tti.oh_crm_service.mapstruct.UserMapStructContext;
import com.tti.oh_crm_service.model.*;
import org.mapstruct.*;
import java.util.List;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, uses = {UserMapper.class})
public abstract class PlaybookMapper {

    // PlaybookTool mappings
    public abstract PlaybookTool toPlaybookTool(CreatePlaybookToolRequest request);
    
    public abstract PlaybookTool toPlaybookTool(UpdatePlaybookToolRequest request);

    public abstract PlaybookToolListView toPlaybookToolListView(PlaybookTool playbookTool);

    public abstract List<PlaybookToolListView> toPlaybookToolListView(List<PlaybookTool> playbookTools);

    @Mapping(target = "sections", source = "playbookToolSections")
    public abstract PlaybookToolDetailView toPlaybookToolDetailView(PlaybookTool playbookTool);
    
    public abstract PlaybookToolSection toPlaybookToolSection(CreatePlaybookToolSectionRequest request);
    
    public abstract PlaybookToolSection toPlaybookToolSection(UpdatePlaybookToolSectionRequest request);

    public abstract PlaybookToolSectionView toPlaybookToolSectionView(PlaybookToolSection section);

    public abstract List<PlaybookToolSectionView> toPlaybookToolSectionView(List<PlaybookToolSection> sections);

    // PlaybookToolHistory mappings
    @Mapping(target = "playbookToolId", source = "playbookTool.id")
    @Mapping(target = "playbookToolName", source = "playbookTool.name")
    @Mapping(target = "playbookToolSectionId", source = "playbookToolSection.id")
    @Mapping(target = "playbookToolSectionName", source = "playbookToolSection.name")
    public abstract PlaybookToolHistoryView toPlaybookToolHistoryView(PlaybookToolHistory history);

    public abstract List<PlaybookToolHistoryView> toPlaybookToolHistoryView(List<PlaybookToolHistory> histories);

    // ModuleStagePlaybook mappings
    public abstract ModuleStagePlaybookView toModuleStagePlaybookView(ModuleStagePlaybook moduleStagePlaybook);

    public abstract List<ModuleStagePlaybookView> toModuleStagePlaybookView(List<ModuleStagePlaybook> moduleStagePlaybooks);

    public abstract ModuleStagePlaybookDetailView toModuleStagePlaybookDetailView(ModuleStagePlaybook moduleStagePlaybook);

    // Custom mapping for ModuleStagePlaybookToolView with section names
    public ModuleStagePlaybookToolView toModuleStagePlaybookToolView(PlaybookTool playbookTool) {
        if (playbookTool == null) {
            return null;
        }

        ModuleStagePlaybookToolView view = new ModuleStagePlaybookToolView();
        view.setId(playbookTool.getId());
        view.setName(playbookTool.getName());
        view.setDescription(playbookTool.getDescription());
        view.setViewCount(playbookTool.getViewCount());

        // Extract section names (only non-trashed sections)
        if (playbookTool.getPlaybookToolSections() != null) {
            List<String> sectionNames = playbookTool.getPlaybookToolSections().stream()
                .filter(section -> section.getIsInTrash() == null || !section.getIsInTrash())
                .map(PlaybookToolSection::getName)
                .toList();
            view.setSectionNames(sectionNames);
        }

        return view;
    }

    public abstract List<ModuleStagePlaybookToolView> toModuleStagePlaybookToolView(List<PlaybookTool> playbookTools);

    // GroupedModulePlaybookProcess mappings
    public abstract GroupedModulePlaybookProcessView toGroupedModulePlaybookProcessView(GroupedModulePlaybookProcess groupedProcess);

    public abstract List<GroupedModulePlaybookProcessView> toGroupedModulePlaybookProcessView(List<GroupedModulePlaybookProcess> groupedProcesses);
}
