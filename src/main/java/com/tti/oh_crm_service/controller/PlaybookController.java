package com.tti.oh_crm_service.controller;

import com.tti.oh_crm_service.entity.*;
import com.tti.oh_crm_service.service.PlaybookService;
import com.tti.oh_crm_service.service.JwtService;
import com.tti.oh_crm_service.utils.LocaleUtils;
import com.tti.oh_crm_service.utils.TokenUtils;
import io.jsonwebtoken.Claims;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Locale;

@RestController
@Transactional
@RequestMapping("/crm/settings/playbooks")
@SecurityRequirement(name = "ohcrm-service")
@Slf4j
public class PlaybookController {

    @Autowired
    private JwtService jwtService;

    @Autowired
    private PlaybookService playbookService;

    // PlaybookTool endpoints

    @PostMapping("/tools")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<PlaybookToolListView>>> createPlaybookTool(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestBody CreatePlaybookToolRequest request) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(playbookService.createPlaybookTool(request, accountId)));
    }

    @PutMapping("/tools")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<PlaybookToolListView>>> updatePlaybookTool(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestBody UpdatePlaybookToolRequest request) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(playbookService.updatePlaybookTool(request, accountId)));
    }

    @GetMapping("/tools")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Page<PlaybookToolListView>>>> getPlaybookToolsList(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestParam(name = "page", required = false, defaultValue = "0") int page,
            @RequestParam(name = "limit", required = false, defaultValue = "10") int limit,
            @RequestParam(name = "search", required = false) String search,
            @RequestParam(name = "sortBy", required = false, defaultValue = "id") String sortBy,
            @RequestParam(name = "sortDirection", required = false, defaultValue = "ASC") String sortDirection) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(playbookService.getPlaybookToolsList(page, limit, search, sortBy, sortDirection, accountId)));
    }

    @GetMapping("/tools/{playbookToolId}")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<PlaybookToolDetailView>>> getPlaybookToolDetail(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @PathVariable Long playbookToolId) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(playbookService.getPlaybookToolDetail(playbookToolId, accountId)));
    }

    @DeleteMapping("/tools/{playbookToolId}")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<String>>> deletePlaybookTool(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @PathVariable Long playbookToolId) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(playbookService.deletePlaybookTool(playbookToolId, accountId)));
    }

    // PlaybookToolSection endpoints

//    @PostMapping("/tools/{playbookToolId}/sections")
//    @Parameters({
//        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
//    })
//    public Mono<ResponseEntity<Response<PlaybookToolSectionView>>> createPlaybookToolSection(
//            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
//            @RequestHeader(value = "Organization-Id") Long organizationId,
//            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
//            @PathVariable Long playbookToolId,
//            @RequestBody CreatePlaybookToolSectionRequest request) {
//        Claims claims = TokenUtils.getToken(jwtService, authToken);
//        long accountId = TokenUtils.getAccountId(claims);
//        String language = LocaleUtils.getLanguage(locale);
//
//        return Mono.justOrEmpty(ResponseEntity.ok(playbookService.createPlaybookToolSection(request, playbookToolId, accountId)));
//    }
//
//    @PutMapping("/tools/{playbookToolId}/sections")
//    @Parameters({
//        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
//    })
//    public Mono<ResponseEntity<Response<PlaybookToolSectionView>>> updatePlaybookToolSection(
//            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
//            @RequestHeader(value = "Organization-Id") Long organizationId,
//            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
//            @PathVariable Long playbookToolId,
//            @RequestBody UpdatePlaybookToolSectionRequest request) {
//        Claims claims = TokenUtils.getToken(jwtService, authToken);
//        long accountId = TokenUtils.getAccountId(claims);
//        String language = LocaleUtils.getLanguage(locale);
//        return Mono.justOrEmpty(ResponseEntity.ok(playbookService.updatePlaybookToolSection(request, playbookToolId, accountId)));
//    }

//    @DeleteMapping("/tools/{playbookToolId}/sections")
//    @Parameters({
//        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
//    })
//    public Mono<ResponseEntity<Response<String>>> deletePlaybookToolSection(
//            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
//            @RequestHeader(value = "Organization-Id") Long organizationId,
//            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
//            @PathVariable Long playbookToolId,
//            @RequestBody Long id
//    ) {
//        Claims claims = TokenUtils.getToken(jwtService, authToken);
//        long accountId = TokenUtils.getAccountId(claims);
//        String language = LocaleUtils.getLanguage(locale);
//        return Mono.justOrEmpty(ResponseEntity.ok(playbookService.deletePlaybookToolSection(id, playbookToolId, accountId)));
//    }

    // Bulk PlaybookToolSection operations

    @PutMapping("/tools/{playbookToolId}/sections/bulk")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<PlaybookToolWithSectionsResponse>>> updatePlaybookToolWithSections(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @PathVariable Long playbookToolId,
            @RequestBody UpdatePlaybookToolWithSectionsRequest request) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(playbookService.updatePlaybookToolWithSections(request, playbookToolId, accountId)));
    }

    // PlaybookToolHistory endpoints

    @GetMapping("/tools/{playbookToolId}/histories")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Page<PlaybookToolHistoryView>>>> getPlaybookToolHistories(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @PathVariable Long playbookToolId,
            @RequestParam(name = "page", required = false, defaultValue = "0") int page,
            @RequestParam(name = "limit", required = false, defaultValue = "10") int limit,
            @RequestParam(name = "sortBy", required = false, defaultValue = "historyDate") String sortBy,
            @RequestParam(name = "sortDirection", required = false, defaultValue = "DESC") String sortDirection) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(playbookService.getPlaybookToolHistories(playbookToolId, page, limit, sortBy, sortDirection, accountId)));
    }

    @PostMapping("/tools/histories/{historyId}/restore")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<String>>> restorePlaybookToolSectionFromHistory(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @PathVariable Long historyId) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(playbookService.restorePlaybookToolSectionFromHistory(historyId, accountId)));
    }

    // GroupedModulePlaybookProcess endpoints

    @GetMapping("/grouped-module-playbook-processes")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<List<GroupedModulePlaybookProcessView>>>> getAllGroupedModulePlaybookProcesses(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(playbookService.getAllGroupedModulePlaybookProcesses(accountId)));
    }

    @PostMapping("/grouped-module-playbook-processes/status")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<GroupedModulePlaybookProcessView>>> updateGroupedModulePlaybookProcessStatus(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestBody UpdateGroupedModulePlaybookProcessStatusRequest request) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(playbookService.updateGroupedModulePlaybookProcessStatus(request, accountId)));
    }

    // ModuleStagePlaybook endpoints

    @GetMapping("/module-stage-playbooks/module/{moduleCode}")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<List<ModuleStagePlaybookView>>>> getModuleStagePlaybooksByModuleCode(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @PathVariable String moduleCode) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(playbookService.getModuleStagePlaybooksByModuleCode(moduleCode, accountId, language)));
    }

    @GetMapping("/module-stage-playbooks/{id}")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<ModuleStagePlaybookDetailView>>> getModuleStagePlaybookDetail(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @PathVariable Long id) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(playbookService.getModuleStagePlaybookDetail(id, accountId)));
    }

    @PutMapping("/module-stage-playbooks/guide-for-success-content")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<ModuleStagePlaybookDetailView>>> updateModuleStagePlaybookGuideForSuccessContent(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestBody UpdateModuleStagePlaybookContentRequest request) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(playbookService.updateModuleStagePlaybookGuideForSuccessContent(request, accountId)));
    }

    @GetMapping("/module-stage-playbooks/{moduleStagePlaybookId}/tools")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Page<ModuleStagePlaybookToolView>>>> getModuleStagePlaybookTools(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @PathVariable Long moduleStagePlaybookId,
            @RequestParam(name = "page", required = false, defaultValue = "0") int page,
            @RequestParam(name = "limit", required = false, defaultValue = "10") int limit,
            @RequestParam(name = "sortBy", required = false, defaultValue = "id") String sortBy,
            @RequestParam(name = "sortDirection", required = false, defaultValue = "ASC") String sortDirection) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(playbookService.getModuleStagePlaybookTools(moduleStagePlaybookId, page, limit, sortBy, sortDirection, accountId)));
    }

    @PostMapping("/module-stage-playbooks/add-tools")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<String>>> addPlaybookToolsToModuleStagePlaybook(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestBody AddPlaybookToolsRequest request) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(playbookService.addPlaybookToolsToModuleStagePlaybook(request, accountId)));
    }

    @PostMapping("/module-stage-playbooks/remove-tools")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<String>>> removePlaybookToolsFromModuleStagePlaybook(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestBody RemovePlaybookToolsRequest request) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(playbookService.removePlaybookToolsFromModuleStagePlaybook(request, accountId)));
    }
}
